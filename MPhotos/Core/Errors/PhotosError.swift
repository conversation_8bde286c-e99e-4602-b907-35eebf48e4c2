//
//  PhotosError.swift
//  MPhotos
//
//  Created by MPhotos Team on 2025/3/13.
//

import Foundation
import Photos

/// MPhotos应用的统一错误类型
enum PhotosError: Error, LocalizedError {
    
    // MARK: - 权限相关错误
    case permissionDenied
    case permissionRestricted
    case permissionNotDetermined
    
    // MARK: - 资源相关错误
    case assetNotFound
    case assetUnavailable
    case assetCorrupted
    case assetTooLarge
    
    // MARK: - 加载相关错误
    case imageLoadFailed
    case videoLoadFailed
    case thumbnailLoadFailed
    case loadTimeout
    
    // MARK: - 缓存相关错误
    case cacheWriteFailed
    case cacheReadFailed
    case cacheFull
    case cacheCorrupted
    
    // MARK: - 网络相关错误
    case networkUnavailable
    case iCloudSyncFailed
    case downloadFailed
    case uploadFailed
    
    // MARK: - 存储相关错误
    case insufficientStorage
    case diskWriteFailed
    case diskReadFailed
    
    // MARK: - 操作相关错误
    case operationCancelled
    case operationFailed
    case invalidOperation
    case concurrentOperationLimit
    
    // MARK: - 系统相关错误
    case memoryWarning
    case systemError(Error)
    case unknownError
    
    // MARK: - 自定义错误
    case custom(String)
    
    // MARK: - LocalizedError实现
    
    var errorDescription: String? {
        switch self {
        // 权限相关
        case .permissionDenied:
            return "访问照片库权限被拒绝"
        case .permissionRestricted:
            return "照片库访问受限"
        case .permissionNotDetermined:
            return "尚未获得照片库访问权限"
            
        // 资源相关
        case .assetNotFound:
            return "找不到指定的照片或视频"
        case .assetUnavailable:
            return "照片或视频暂时不可用"
        case .assetCorrupted:
            return "照片或视频文件已损坏"
        case .assetTooLarge:
            return "文件过大，无法处理"
            
        // 加载相关
        case .imageLoadFailed:
            return "图片加载失败"
        case .videoLoadFailed:
            return "视频加载失败"
        case .thumbnailLoadFailed:
            return "缩略图加载失败"
        case .loadTimeout:
            return "加载超时"
            
        // 缓存相关
        case .cacheWriteFailed:
            return "缓存写入失败"
        case .cacheReadFailed:
            return "缓存读取失败"
        case .cacheFull:
            return "缓存空间已满"
        case .cacheCorrupted:
            return "缓存数据损坏"
            
        // 网络相关
        case .networkUnavailable:
            return "网络连接不可用"
        case .iCloudSyncFailed:
            return "iCloud同步失败"
        case .downloadFailed:
            return "下载失败"
        case .uploadFailed:
            return "上传失败"
            
        // 存储相关
        case .insufficientStorage:
            return "存储空间不足"
        case .diskWriteFailed:
            return "磁盘写入失败"
        case .diskReadFailed:
            return "磁盘读取失败"
            
        // 操作相关
        case .operationCancelled:
            return "操作已取消"
        case .operationFailed:
            return "操作失败"
        case .invalidOperation:
            return "无效操作"
        case .concurrentOperationLimit:
            return "并发操作数量超限"
            
        // 系统相关
        case .memoryWarning:
            return "内存不足警告"
        case .systemError(let error):
            return "系统错误：\(error.localizedDescription)"
        case .unknownError:
            return "未知错误"
            
        // 自定义错误
        case .custom(let message):
            return message
        }
    }
    
    var failureReason: String? {
        switch self {
        case .permissionDenied:
            return "用户拒绝了照片库访问权限"
        case .assetNotFound:
            return "照片可能已被删除或移动"
        case .networkUnavailable:
            return "设备未连接到网络"
        case .insufficientStorage:
            return "设备存储空间不足"
        case .memoryWarning:
            return "应用内存使用过高"
        default:
            return nil
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .permissionDenied:
            return "请在设置中允许访问照片库"
        case .networkUnavailable:
            return "请检查网络连接后重试"
        case .insufficientStorage:
            return "请清理设备存储空间"
        case .memoryWarning:
            return "请关闭其他应用或重启设备"
        case .loadTimeout:
            return "请检查网络连接或稍后重试"
        default:
            return "请稍后重试"
        }
    }
}

// MARK: - 便利方法

extension PhotosError {
    
    /// 从PHPhotosError转换
    static func from(phError: Error) -> PhotosError {
        guard let phError = phError as? PHPhotosError else {
            return .systemError(phError)
        }
        
        switch phError.code {
        case .userCancelled:
            return .operationCancelled
        case .libraryVolumeOffline:
            return .assetUnavailable
        case .relinquishingLibraryBundleToWriter:
            return .operationFailed
        case .switchingSystemPhotoLibrary:
            return .operationFailed
        case .networkAccessRequired:
            return .networkUnavailable
        case .networkError:
            return .networkUnavailable
        case .identifierNotFound:
            return .assetNotFound
        case .multipleIdentifiersFound:
            return .invalidOperation
        case .changeNotSupported:
            return .invalidOperation
        case .operationInterrupted:
            return .operationCancelled
        case .invalidResource:
            return .assetCorrupted
        case .missingResource:
            return .assetNotFound
        case .notEnoughSpace:
            return .insufficientStorage
        case .requestNotSupportedForAsset:
            return .invalidOperation
        case .accessRestricted:
            return .permissionRestricted
        case .accessUserDenied:
            return .permissionDenied
        case .libraryInFileProviderSyncRoot:
            return .operationFailed
        case .persistentChangeTokenExpired:
            return .operationFailed
        case .persistentChangeDetailsUnavailable:
            return .operationFailed
        @unknown default:
            return .systemError(phError)
        }
    }
    
    /// 是否为可重试的错误
    var isRetryable: Bool {
        switch self {
        case .loadTimeout, .networkUnavailable, .iCloudSyncFailed, 
             .downloadFailed, .uploadFailed, .cacheWriteFailed, 
             .diskWriteFailed, .operationFailed:
            return true
        default:
            return false
        }
    }
    
    /// 是否为致命错误
    var isFatal: Bool {
        switch self {
        case .permissionDenied, .permissionRestricted, .assetCorrupted,
             .insufficientStorage, .systemError, .unknownError:
            return true
        default:
            return false
        }
    }
}
