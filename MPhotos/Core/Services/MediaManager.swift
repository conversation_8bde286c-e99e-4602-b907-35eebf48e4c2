//
//  MediaManager.swift
//  MPhotos
//
//  Created by MPhotos Team on 2025/3/13.
//

import UIKit
import Photos
import Combine

/// 统一媒体管理器 - 简化版本，专注于核心功能
final class MediaManager: MediaManaging {
    
    // MARK: - 依赖
    private let cacheManager: CacheManaging
    private let memoryMonitor = MemoryMonitor()
    private let performanceMonitor = PerformanceMonitor.shared
    
    // MARK: - 私有属性
    private let imageManager = PHImageManager.default()
    private let queue = DispatchQueue(label: "com.mphotos.media", qos: .userInitiated)
    
    /// 活跃的请求记录（用于取消）
    private var activeRequests: [String: PHImageRequestID] = [:]
    private let requestsQueue = DispatchQueue(label: "com.mphotos.requests", attributes: .concurrent)
    
    /// 观察者模式支持
    private var observers: [WeakRef<any MediaManagerObserver>] = []
    private let observersQueue = DispatchQueue(label: "com.mphotos.observers", attributes: .concurrent)
    
    // MARK: - 配置
    private struct Config {
        static let thumbnailQuality: CGFloat = 0.8
        static let fullImageQuality: CGFloat = 0.95
        static let requestTimeout: TimeInterval = 30.0
        static let maxConcurrentRequests: Int = 10
    }
    
    // MARK: - 初始化
    init(cacheManager: CacheManaging = LightweightCacheManager.shared) {
        self.cacheManager = cacheManager
        setupMemoryMonitoring()
    }
    
    // MARK: - MediaManaging协议实现
    
    /// 加载缩略图
    func loadThumbnail(for asset: PHAsset, targetSize: CGSize, completion: @escaping (Result<UIImage, PhotosError>) -> Void) {
        let cacheKey = generateCacheKey(for: asset, size: targetSize)

        // 1. 检查缓存
        Task {
            if let cachedImage = await cacheManager.image(for: cacheKey) {
                DispatchQueue.main.async {
                    completion(.success(cachedImage))
                }
                return
            }

            // 继续执行后续逻辑
            await loadImageFromPHImageManager(asset: asset, targetSize: targetSize, cacheKey: cacheKey, completion: completion)
        }
    }

    /// 从PHImageManager加载图片的辅助方法
    private func loadImageFromPHImageManager(asset: PHAsset, targetSize: CGSize, cacheKey: String, completion: @escaping (Result<UIImage, PhotosError>) -> Void) async {
        
        // 2. 从PHImageManager请求
        requestImage(for: asset, targetSize: targetSize, cacheKey: cacheKey, completion: completion)
    }
    
    /// 加载全尺寸图片
    func loadFullImage(for asset: PHAsset, completion: @escaping (Result<UIImage, PhotosError>) -> Void) {
        let targetSize = PHImageManagerMaximumSize
        let cacheKey = generateCacheKey(for: asset, size: targetSize)

        // 1. 检查缓存
        Task {
            if let cachedImage = await cacheManager.image(for: cacheKey) {
                DispatchQueue.main.async {
                    completion(.success(cachedImage))
                }
                return
            }

            // 继续执行后续逻辑
            await loadImageFromPHImageManager(asset: asset, targetSize: targetSize, cacheKey: cacheKey, completion: completion)
        }
    }
        
        // 2. 从PHImageManager请求高质量图片
        let options = PHImageRequestOptions()
        options.deliveryMode = .highQualityFormat
        options.isNetworkAccessAllowed = true
        options.isSynchronous = false
        
        let requestID = imageManager.requestImage(
            for: asset,
            targetSize: targetSize,
            contentMode: .aspectFit,
            options: options
        ) { [weak self] image, info in
            self?.handleImageResult(image: image, info: info, cacheKey: cacheKey, completion: completion)
        }
        
        // 记录请求
        recordRequest(for: asset, requestID: requestID)
    }
    
    /// 取消指定资源的请求
    func cancelRequest(for asset: PHAsset) {
        let assetKey = asset.localIdentifier
        
        requestsQueue.async(flags: .barrier) { [weak self] in
            if let requestID = self?.activeRequests.removeValue(forKey: assetKey) {
                self?.imageManager.cancelImageRequest(requestID)
            }
        }
    }
    
    /// 预加载图片
    func preloadImages(for assets: [PHAsset], targetSize: CGSize) {
        Task {
            let options = PHImageRequestOptions()
            options.deliveryMode = .fastFormat
            options.isNetworkAccessAllowed = false
            options.isSynchronous = false

            for asset in assets.prefix(Config.maxConcurrentRequests) {
                let cacheKey = generateCacheKey(for: asset, size: targetSize)

                // 跳过已缓存的图片
                if await cacheManager.image(for: cacheKey) != nil {
                    continue
                }

                // 继续预加载逻辑
                await preloadSingleImage(asset: asset, targetSize: targetSize, cacheKey: cacheKey, options: options)
            }
        }
    }

    /// 预加载单个图片的辅助方法
    private func preloadSingleImage(asset: PHAsset, targetSize: CGSize, cacheKey: String, options: PHImageRequestOptions) async {
            
            let requestID = imageManager.requestImage(
                for: asset,
                targetSize: targetSize,
                contentMode: .aspectFill,
                options: options
            ) { [weak self] image, info in
                if let image = image {
                    self?.cacheManager.store(image, for: cacheKey)
                }
            }
            
            recordRequest(for: asset, requestID: requestID)
        }
    }
    
    // MARK: - 观察者模式
    
    /// 添加观察者
    func addObserver(_ observer: any MediaManagerObserver) {
        observersQueue.async(flags: .barrier) { [weak self] in
            self?.observers.append(WeakRef(observer))
            self?.cleanupObservers()
        }
    }
    
    /// 移除观察者
    func removeObserver(_ observer: any MediaManagerObserver) {
        observersQueue.async(flags: .barrier) { [weak self] in
            self?.observers.removeAll { $0.value === observer }
        }
    }
    
    // MARK: - 私有方法
    
    /// 请求图片的核心方法
    private func requestImage(for asset: PHAsset, targetSize: CGSize, cacheKey: String, completion: @escaping (Result<UIImage, PhotosError>) -> Void) {
        let options = PHImageRequestOptions()
        options.deliveryMode = .opportunistic
        options.isNetworkAccessAllowed = true
        options.isSynchronous = false
        
        let requestID = performanceMonitor.measureTime(operation: "图片请求") {
            return imageManager.requestImage(
                for: asset,
                targetSize: targetSize,
                contentMode: .aspectFill,
                options: options
            ) { [weak self] image, info in
                self?.handleImageResult(image: image, info: info, cacheKey: cacheKey, completion: completion)
            }
        }
        
        recordRequest(for: asset, requestID: requestID)
    }
    
    /// 处理图片请求结果
    private func handleImageResult(image: UIImage?, info: [AnyHashable: Any]?, cacheKey: String, completion: @escaping (Result<UIImage, PhotosError>) -> Void) {
        
        // 检查是否被取消
        if let info = info, let cancelled = info[PHImageCancelledKey] as? Bool, cancelled {
            DispatchQueue.main.async {
                completion(.failure(.operationCancelled))
            }
            return
        }
        
        // 检查错误
        if let error = info?[PHImageErrorKey] as? Error {
            let photosError = PhotosError.from(phError: error)
            DispatchQueue.main.async {
                completion(.failure(photosError))
            }
            notifyObservers { $0.mediaManager(self, didFailWithError: photosError) }
            return
        }
        
        // 处理成功结果
        guard let image = image else {
            DispatchQueue.main.async {
                completion(.failure(.imageLoadFailed))
            }
            return
        }
        
        // 缓存图片
        cacheManager.store(image, for: cacheKey)
        
        // 返回结果
        DispatchQueue.main.async {
            completion(.success(image))
        }
        
        // 通知观察者
        notifyObservers { $0.mediaManager(self, didLoadImage: image, for: cacheKey) }
    }
    
    /// 生成缓存键
    private func generateCacheKey(for asset: PHAsset, size: CGSize) -> String {
        let sizeString = "\(Int(size.width))x\(Int(size.height))"
        return "\(asset.localIdentifier)_\(sizeString)"
    }
    
    /// 记录活跃请求
    private func recordRequest(for asset: PHAsset, requestID: PHImageRequestID) {
        let assetKey = asset.localIdentifier
        
        requestsQueue.async(flags: .barrier) { [weak self] in
            self?.activeRequests[assetKey] = requestID
        }
    }
    
    /// 设置内存监控
    private func setupMemoryMonitoring() {
        memoryMonitor.onMemoryWarning = { [weak self] in
            self?.handleMemoryWarning()
        }
        memoryMonitor.startMonitoring()
    }
    
    /// 处理内存警告
    private func handleMemoryWarning() {
        cacheManager.clearMemoryCache()
        
        // 取消所有活跃请求
        requestsQueue.async(flags: .barrier) { [weak self] in
            guard let self = self else { return }
            
            for requestID in self.activeRequests.values {
                self.imageManager.cancelImageRequest(requestID)
            }
            self.activeRequests.removeAll()
        }
        
        notifyObservers { $0.mediaManagerDidReceiveMemoryWarning(self) }
    }
    
    /// 通知观察者
    private func notifyObservers(_ block: (MediaManagerObserver) -> Void) {
        observersQueue.async { [weak self] in
            self?.observers.compactMap { $0.value }.forEach(block)
        }
    }
    
    /// 清理失效的观察者
    private func cleanupObservers() {
        observers = observers.filter { $0.value != nil }
    }
}

// MARK: - 观察者协议

protocol MediaManagerObserver: AnyObject {
    func mediaManager(_ manager: MediaManager, didLoadImage image: UIImage, for key: String)
    func mediaManager(_ manager: MediaManager, didFailWithError error: PhotosError)
    func mediaManagerDidReceiveMemoryWarning(_ manager: MediaManager)
}



// MARK: - 扩展：统计信息

extension MediaManager {
    
    /// 获取管理器状态
    func getManagerStatus() -> MediaManagerStatus {
        let activeRequestCount = requestsQueue.sync {
            return activeRequests.count
        }
        
        let observerCount = observersQueue.sync {
            return observers.compactMap { $0.value }.count
        }
        
        return MediaManagerStatus(
            activeRequests: activeRequestCount,
            observers: observerCount,
            cacheStatistics: cacheManager.getCacheStatistics(),
            memoryInfo: memoryMonitor.getFormattedMemoryInfo()
        )
    }
}

// MARK: - 状态数据结构

struct MediaManagerStatus {
    let activeRequests: Int
    let observers: Int
    let cacheStatistics: CacheStatistics
    let memoryInfo: MemoryInfo
    
    var isHealthy: Bool {
        return activeRequests < 50 && memoryInfo.isHealthy
    }
}
